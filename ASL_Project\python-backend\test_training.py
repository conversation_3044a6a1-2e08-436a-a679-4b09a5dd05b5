#!/usr/bin/env python3
"""
Test script to verify training functionality
"""

import os
import pandas as pd
from training import ASLModelTrainer

def test_training_setup():
    """Test the training setup and data loading"""
    print("🧪 Testing ASL Training Setup")
    print("=" * 50)
    
    # Initialize trainer
    trainer = ASLModelTrainer()
    
    # Check sign mapping
    print(f"📊 Sign mapping loaded: {len(trainer.sign_to_index)} signs")
    print(f"First 10 signs: {list(trainer.sign_to_index.keys())[:10]}")
    
    # Check train.csv
    train_csv_path = os.path.join("data", "train.csv")
    if os.path.exists(train_csv_path):
        df = pd.read_csv(train_csv_path)
        print(f"📁 Training data: {len(df)} samples")
        
        # Check for recordings
        recording_files = df[df['path'].str.startswith('recordings/')]['path'].tolist()
        print(f"🎥 Recorded samples: {len(recording_files)}")
        
        if recording_files:
            print("Recent recordings:")
            for file_path in recording_files[-5:]:  # Show last 5
                if os.path.exists(file_path):
                    print(f"  ✅ {file_path}")
                else:
                    print(f"  ❌ {file_path} (missing)")
        
        # Check sign distribution
        sign_counts = df['sign'].value_counts()
        print(f"📈 Unique signs in dataset: {len(sign_counts)}")
        print("Top 10 signs:")
        for sign, count in sign_counts.head(10).items():
            print(f"  {sign}: {count}")
            
    else:
        print("❌ No train.csv found")
    
    # Check recordings directory
    recordings_dir = "recordings"
    if os.path.exists(recordings_dir):
        parquet_files = [f for f in os.listdir(recordings_dir) if f.endswith('.parquet')]
        print(f"📂 Parquet files in recordings/: {len(parquet_files)}")
        for file in parquet_files:
            print(f"  📄 {file}")
    else:
        print("❌ No recordings directory found")

def test_data_loading():
    """Test loading a sample parquet file"""
    print("\n🔍 Testing Data Loading")
    print("=" * 30)
    
    trainer = ASLModelTrainer()
    
    # Find a sample parquet file
    recordings_dir = "recordings"
    if os.path.exists(recordings_dir):
        parquet_files = [f for f in os.listdir(recordings_dir) if f.endswith('.parquet')]
        if parquet_files:
            sample_file = os.path.join(recordings_dir, parquet_files[0])
            print(f"📄 Testing with: {sample_file}")
            
            data = trainer.load_data_from_file(sample_file)
            if data is not None:
                print(f"✅ Data loaded successfully: shape {data.shape}")
                print(f"   Frames: {data.shape[0]}")
                print(f"   Landmarks: {data.shape[1]}")
                print(f"   Coordinates: {data.shape[2]}")
            else:
                print("❌ Failed to load data")
        else:
            print("❌ No parquet files found in recordings/")
    else:
        print("❌ No recordings directory found")

def test_dataset_creation():
    """Test creating TensorFlow dataset"""
    print("\n🏗️ Testing Dataset Creation")
    print("=" * 35)
    
    trainer = ASLModelTrainer()
    
    try:
        train_ds, val_ds = trainer.create_dataset()
        
        if train_ds is not None and val_ds is not None:
            print("✅ Datasets created successfully")
            
            # Get dataset info
            train_size = sum(1 for _ in train_ds)
            val_size = sum(1 for _ in val_ds)
            
            print(f"📊 Training batches: {train_size}")
            print(f"📊 Validation batches: {val_size}")
            
            # Sample a batch
            for batch in train_ds.take(1):
                features, labels = batch
                print(f"📦 Sample batch - Features: {features.shape}, Labels: {labels.shape}")
                break
                
        else:
            print("❌ Failed to create datasets")
            
    except Exception as e:
        print(f"❌ Error creating datasets: {e}")

def main():
    """Run all tests"""
    test_training_setup()
    test_data_loading()
    test_dataset_creation()
    
    print("\n🎯 Test Summary")
    print("=" * 20)
    print("If all tests passed, the training system is ready!")
    print("You can now:")
    print("1. Record more signs using the frontend")
    print("2. Start training via the API endpoint")
    print("3. Monitor training progress")

if __name__ == "__main__":
    main()
