"""
Training module for ASL sign language recognition model
Adapted from the Jupyter notebooks and streamlit app
"""

import os
import json
import pandas as pd
import numpy as np
import tensorflow as tf
from tensorflow.keras import layers, models, optimizers
from datetime import datetime
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ASLModelTrainer:
    def __init__(self, data_dir="data", models_dir="models"):
        self.data_dir = data_dir
        self.models_dir = models_dir
        
        # Training constants
        self.COLUMNS_OF_INTEREST = ['x', 'y', 'z']
        self.TOTAL_LANDMARKS = 543
        self.IMPORTANT_LANDMARKS = [0, 9, 11, 13, 14, 17, 117, 118, 119, 199, 346, 347, 348] + list(range(468, 543))
        self.BATCHING_SIZE = 32  # Smaller batch size for local training
        self.EPOCHS = 50  # Reduced epochs for faster training
        
        # Ensure directories exist
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.models_dir, exist_ok=True)
        
        # Load sign mapping
        self.sign_to_index = self._load_sign_mapping()
        self.index_to_sign = {v: k for k, v in self.sign_to_index.items()}
        self.number_of_signs = len(self.sign_to_index)
        
        logger.info(f"Initialized trainer with {self.number_of_signs} signs")
    
    def _load_sign_mapping(self):
        """Load or create sign to index mapping"""
        mapping_file = os.path.join(self.data_dir, "sign_to_index_mapping.json")
        
        if os.path.exists(mapping_file):
            with open(mapping_file, 'r') as f:
                return json.load(f)
        else:
            # Create mapping from train.csv
            train_csv_path = os.path.join(self.data_dir, "train.csv")
            if os.path.exists(train_csv_path):
                df = pd.read_csv(train_csv_path)
                unique_signs = sorted(df['sign'].unique())
                mapping = {sign: idx for idx, sign in enumerate(unique_signs)}
                
                # Save mapping
                with open(mapping_file, 'w') as f:
                    json.dump(mapping, f, indent=2)
                
                logger.info(f"Created sign mapping with {len(mapping)} signs")
                return mapping
            else:
                logger.error(f"No train.csv found at {train_csv_path}")
                return {}
    
    def load_data_from_file(self, file_path):
        """Load and reshape data from a parquet file"""
        try:
            # Handle both absolute and relative paths
            if not os.path.isabs(file_path):
                # If path starts with recordings/, it's relative to current directory
                if file_path.startswith('recordings/'):
                    full_path = file_path
                else:
                    full_path = os.path.join(self.data_dir, file_path)
            else:
                full_path = file_path

            if not os.path.exists(full_path):
                logger.error(f"File not found: {full_path}")
                return None

            data = pd.read_parquet(full_path, columns=self.COLUMNS_OF_INTEREST)
            frames_count = int(len(data) / self.TOTAL_LANDMARKS)
            data_as_floats = data.values.astype(np.float32)
            reshaped_data = data_as_floats.reshape(frames_count, self.TOTAL_LANDMARKS, len(self.COLUMNS_OF_INTEREST))

            return reshaped_data
        except Exception as e:
            logger.error(f"Error loading data from {file_path}: {e}")
            return None
    
    def preprocess_data(self, data, labels):
        """Preprocess the data: select landmarks, replace missing values"""
        # Select important landmarks
        processed_data = tf.gather(data, self.IMPORTANT_LANDMARKS, axis=2)
        # Replace missing values with 0
        processed_data = tf.where(tf.math.is_nan(processed_data), tf.zeros_like(processed_data), processed_data)
        # Reshape: concatenate x, y, z coordinates
        return tf.concat([processed_data[..., i] for i in range(3)], -1), labels
    
    def create_dataset(self):
        """Create TensorFlow dataset from parquet files"""
        train_csv_path = os.path.join(self.data_dir, "train.csv")
        
        if not os.path.exists(train_csv_path):
            logger.error(f"No train.csv found at {train_csv_path}")
            return None, None
        
        # Load dataset from CSV
        dataframe = pd.read_csv(train_csv_path)
        logger.info(f"Loaded {len(dataframe)} training samples")
        
        # Filter out files that don't exist
        valid_rows = []
        for _, row in dataframe.iterrows():
            file_path = row['path']
            if not os.path.isabs(file_path):
                # If path starts with recordings/, it's relative to current directory
                if file_path.startswith('recordings/'):
                    full_path = file_path
                else:
                    full_path = os.path.join(self.data_dir, file_path)
            else:
                full_path = file_path

            if os.path.exists(full_path):
                valid_rows.append(row)
            else:
                logger.warning(f"File not found, skipping: {full_path}")
        
        if not valid_rows:
            logger.error("No valid training files found")
            return None, None
        
        valid_df = pd.DataFrame(valid_rows)
        logger.info(f"Found {len(valid_df)} valid training files")
        
        # Create features and labels
        features = []
        labels = []
        
        for _, row in valid_df.iterrows():
            data = self.load_data_from_file(row['path'])
            if data is not None:
                features.append(data)
                sign_label = row['sign'].lower()
                if sign_label in self.sign_to_index:
                    labels.append(self.sign_to_index[sign_label])
                else:
                    logger.warning(f"Unknown sign: {sign_label}")
                    continue
        
        if not features:
            logger.error("No valid features loaded")
            return None, None
        
        logger.info(f"Loaded {len(features)} valid samples")
        
        # Convert to ragged tensors for variable-length sequences
        features_tensor = tf.ragged.constant(features, dtype=tf.float32)
        labels_tensor = tf.constant(labels, dtype=tf.int32)
        
        # Create dataset
        dataset = tf.data.Dataset.from_tensor_slices((features_tensor, labels_tensor))
        dataset = dataset.map(self.preprocess_data, num_parallel_calls=tf.data.AUTOTUNE)
        
        # Split into train and validation
        dataset_size = len(features)
        val_size = max(1, dataset_size // 10)  # 10% for validation
        
        val_ds = dataset.take(val_size).batch(self.BATCHING_SIZE).cache().prefetch(tf.data.AUTOTUNE)
        train_ds = dataset.skip(val_size).batch(self.BATCHING_SIZE).cache().shuffle(20).prefetch(tf.data.AUTOTUNE)
        
        logger.info(f"Created datasets: {dataset_size - val_size} train, {val_size} validation")
        
        return train_ds, val_ds
    
    def create_model(self):
        """Create the ASL recognition model"""
        input_layer = tf.keras.Input(
            shape=(None, 3 * len(self.IMPORTANT_LANDMARKS)), 
            ragged=True, 
            name="input_layer"
        )
        
        # Dense layers with normalization and dropout
        sequence = input_layer
        for units in [512, 256]:
            sequence = layers.Dense(units)(sequence)
            sequence = layers.LayerNormalization()(sequence)
            sequence = layers.Activation("relu")(sequence)
            sequence = layers.Dropout(0.1)(sequence)
        
        # LSTM layer for sequential data processing
        sequence = layers.LSTM(250, name="lstm_layer")(sequence)
        
        # Output layer for classification
        output_layer = layers.Dense(
            self.number_of_signs, 
            activation="softmax", 
            name="output_layer"
        )(sequence)
        
        # Define the model
        model = models.Model(inputs=input_layer, outputs=output_layer, name="sign_language_model")
        
        return model
    
    def train_model(self, progress_callback=None):
        """Train the ASL recognition model"""
        logger.info("Starting model training...")
        
        # Create datasets
        train_ds, val_ds = self.create_dataset()
        if train_ds is None or val_ds is None:
            logger.error("Failed to create datasets")
            return None
        
        # Create model
        model = self.create_model()
        model.summary()
        
        # Callbacks
        callbacks = [
            tf.keras.callbacks.EarlyStopping(
                monitor="val_accuracy", 
                patience=10, 
                restore_best_weights=True
            ),
            tf.keras.callbacks.ReduceLROnPlateau(
                monitor="val_accuracy", 
                factor=0.5, 
                patience=3
            )
        ]
        
        # Add progress callback if provided
        if progress_callback:
            callbacks.append(progress_callback)
        
        # Compile model
        learning_rate_schedule = optimizers.schedules.PiecewiseConstantDecay(
            [10, 15], [1e-3, 1e-4, 1e-5]
        )
        model.compile(
            optimizer=optimizers.Adam(learning_rate=learning_rate_schedule),
            loss="sparse_categorical_crossentropy",
            metrics=["accuracy", "sparse_top_k_categorical_accuracy"]
        )
        
        # Train model
        history = model.fit(
            train_ds,
            validation_data=val_ds,
            callbacks=callbacks,
            epochs=self.EPOCHS,
            verbose=1
        )
        
        # Save model
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_path = os.path.join(self.models_dir, f"model_{timestamp}.h5")
        model.save(model_path)
        logger.info(f"Model saved to {model_path}")
        
        # Convert to TensorFlow Lite
        tflite_path = os.path.join(self.models_dir, "model.tflite")
        self._convert_to_tflite(model, tflite_path)
        
        return {
            "model_path": model_path,
            "tflite_path": tflite_path,
            "history": history.history,
            "final_accuracy": history.history['accuracy'][-1],
            "final_val_accuracy": history.history['val_accuracy'][-1]
        }
    
    def _convert_to_tflite(self, model, output_path):
        """Convert Keras model to TensorFlow Lite"""
        try:
            converter = tf.lite.TFLiteConverter.from_keras_model(model)
            tflite_model = converter.convert()
            
            with open(output_path, 'wb') as f:
                f.write(tflite_model)
            
            logger.info(f"TensorFlow Lite model saved to {output_path}")
        except Exception as e:
            logger.error(f"Error converting to TensorFlow Lite: {e}")
