import React, { useState } from 'react';
import styled from 'styled-components';
import { 
  Brain, 
  ArrowLeft,
  Mail,
  MessageSquare,
  Send,
  MapPin,
  Phone,
  Globe
} from 'lucide-react';

const ContactContainer = styled.div`
  min-height: 100vh;
  background: var(--bg-primary);
  position: relative;
  overflow-x: hidden;
  
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(147, 51, 234, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
  }
`;

const Navigation = styled.nav`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-neural);
  padding: var(--space-4) 0;
  transition: var(--transition-normal);
`;

const NavContainer = styled.div`
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  @media (max-width: 768px) {
    padding: 0 var(--space-4);
  }
`;

const Logo = styled.div`
  font-family: var(--font-primary);
  font-size: 1.5rem;
  font-weight: 700;
  background: var(--text-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: flex;
  align-items: center;
  gap: var(--space-3);

  @media (max-width: 768px) {
    font-size: 1.25rem;
    gap: var(--space-2);
  }
`;

const LogoIcon = styled.div`
  width: 40px;
  height: 40px;
  background: var(--bg-neural);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-neural);
`;

const BackButton = styled.button`
  background: var(--bg-glass);
  color: var(--text-secondary);
  border: 1px solid var(--border-neural);
  padding: var(--space-3) var(--space-5);
  border-radius: var(--radius-xl);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  backdrop-filter: blur(10px);
  
  &:hover {
    background: var(--primary-50);
    color: var(--primary-600);
    border-color: var(--primary-300);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
  }
`;

const MainContent = styled.main`
  padding: var(--space-24) var(--space-6) var(--space-20);
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
  
  @media (max-width: 768px) {
    padding: var(--space-20) var(--space-4) var(--space-16);
  }
`;

const PageTitle = styled.h1`
  font-family: var(--font-primary);
  font-size: 2.75rem;
  font-weight: 700;
  background: var(--text-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
  margin-bottom: var(--space-4);
  letter-spacing: -0.02em;

  @media (max-width: 768px) {
    font-size: 2.25rem;
  }
`;

const PageSubtitle = styled.p`
  font-size: 1.125rem;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: var(--space-16);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;

  @media (max-width: 768px) {
    font-size: 1rem;
    margin-bottom: var(--space-12);
  }
`;

const ContactGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-12);
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: var(--space-8);
  }
`;

const ContactInfo = styled.div`
  background: var(--bg-glass);
  border-radius: var(--radius-2xl);
  padding: var(--space-10);
  border: 1px solid var(--border-neural);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
`;

const ContactForm = styled.form`
  background: var(--bg-glass);
  border-radius: var(--radius-2xl);
  padding: var(--space-10);
  border: 1px solid var(--border-neural);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
`;

const SectionTitle = styled.h2`
  font-family: var(--font-primary);
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-8);
  display: flex;
  align-items: center;
  gap: var(--space-3);
`;

const ContactItem = styled.div`
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  transition: var(--transition-normal);
  
  &:hover {
    background: var(--primary-50);
  }
`;

const ContactIcon = styled.div`
  width: 48px;
  height: 48px;
  background: var(--bg-neural);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-neural);
`;

const ContactText = styled.div`
  h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-1);
  }
  
  p {
    color: var(--text-secondary);
    font-size: 0.9rem;
  }
`;

const FormGroup = styled.div`
  margin-bottom: var(--space-6);
`;

const Label = styled.label`
  display: block;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  font-size: 0.9rem;
`;

const Input = styled.input`
  width: 100%;
  padding: var(--space-4);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  font-size: 1rem;
  transition: var(--transition-normal);
  background: var(--bg-primary);
  
  &:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: var(--shadow-glow);
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: var(--space-4);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  font-size: 1rem;
  transition: var(--transition-normal);
  background: var(--bg-primary);
  min-height: 120px;
  resize: vertical;
  
  &:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: var(--shadow-glow);
  }
`;

const SubmitButton = styled.button`
  background: var(--bg-neural);
  color: white;
  border: none;
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-xl);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  box-shadow: var(--shadow-neural);
  width: 100%;
  justify-content: center;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl), var(--shadow-glow);
  }
`;

const ContactPage = ({ onBackToHome }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission
    console.log('Form submitted:', formData);
    alert('Thank you for your message! We\'ll get back to you soon.');
    setFormData({ name: '', email: '', subject: '', message: '' });
  };

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <ContactContainer>
      <Navigation>
        <NavContainer>
          <Logo>
            <LogoIcon>
              <Brain size={24} />
            </LogoIcon>
            ASL Neural
          </Logo>
          <BackButton onClick={onBackToHome}>
            <ArrowLeft size={18} />
            Back to Home
          </BackButton>
        </NavContainer>
      </Navigation>

      <MainContent>
        <PageTitle>Contact Us</PageTitle>
        <PageSubtitle>
          Get in touch with our team to learn more about ASL Neural technology 
          or discuss partnership opportunities
        </PageSubtitle>

        <ContactGrid>
          <ContactInfo>
            <SectionTitle>
              <MessageSquare size={24} />
              Get in Touch
            </SectionTitle>

            <ContactItem>
              <ContactIcon>
                <Mail size={20} />
              </ContactIcon>
              <ContactText>
                <h3>Email</h3>
                <p><EMAIL></p>
              </ContactText>
            </ContactItem>

            <ContactItem>
              <ContactIcon>
                <Phone size={20} />
              </ContactIcon>
              <ContactText>
                <h3>Phone</h3>
                <p>+****************</p>
              </ContactText>
            </ContactItem>

            <ContactItem>
              <ContactIcon>
                <MapPin size={20} />
              </ContactIcon>
              <ContactText>
                <h3>Address</h3>
                <p>123 AI Innovation Drive<br />San Francisco, CA 94105</p>
              </ContactText>
            </ContactItem>

            <ContactItem>
              <ContactIcon>
                <Globe size={20} />
              </ContactIcon>
              <ContactText>
                <h3>Website</h3>
                <p>www.aslneural.ai</p>
              </ContactText>
            </ContactItem>
          </ContactInfo>

          <ContactForm onSubmit={handleSubmit}>
            <SectionTitle>
              <Send size={24} />
              Send Message
            </SectionTitle>

            <FormGroup>
              <Label htmlFor="name">Name</Label>
              <Input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
              />
            </FormGroup>

            <FormGroup>
              <Label htmlFor="email">Email</Label>
              <Input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
              />
            </FormGroup>

            <FormGroup>
              <Label htmlFor="subject">Subject</Label>
              <Input
                type="text"
                id="subject"
                name="subject"
                value={formData.subject}
                onChange={handleChange}
                required
              />
            </FormGroup>

            <FormGroup>
              <Label htmlFor="message">Message</Label>
              <TextArea
                id="message"
                name="message"
                value={formData.message}
                onChange={handleChange}
                required
              />
            </FormGroup>

            <SubmitButton type="submit">
              <Send size={18} />
              Send Message
            </SubmitButton>
          </ContactForm>
        </ContactGrid>
      </MainContent>
    </ContactContainer>
  );
};

export default ContactPage;
