import { useState, useRef, useCallback, useEffect } from 'react';

const BACKEND_URL = 'ws://localhost:8001/ws/detect';

export const useSignDetection = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [prediction, setPrediction] = useState(null);
  const [lastPrediction, setLastPrediction] = useState(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingStatus, setRecordingStatus] = useState('');
  const [processedFrame, setProcessedFrame] = useState(null);
  const [signMatched, setSignMatched] = useState(false);
  const [targetSign, setTargetSign] = useState('');
  const [predictionHistory, setPredictionHistory] = useState([]);
  
  const wsRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const frameIntervalRef = useRef(null);
  
  const connect = useCallback(() => {
    try {
      wsRef.current = new WebSocket(BACKEND_URL);
      
      wsRef.current.onopen = () => {
        console.log('Connected to sign detection backend');
        setIsConnected(true);
        setRecordingStatus('');
      };
      
      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log('Received from backend:', data.type, data.prediction);

          switch (data.type) {
            case 'frame_processed':
              // Always update processed frame
              setProcessedFrame(data.processed_frame);
              setSignMatched(data.sign_matched || false);
              setTargetSign(data.target_sign || '');

              // Update prediction - keep last prediction if new one is null
              if (data.prediction) {
                setPrediction(data.prediction);
                setLastPrediction(data.prediction);

                // Add to prediction history (keep last 5)
                setPredictionHistory(prev => {
                  const newHistory = [data.prediction, ...prev.slice(0, 4)];
                  return newHistory;
                });

                console.log(`Detected: ${data.prediction.sign} (${Math.round(data.prediction.confidence * 100)}%)`);
                
                // Show recording session status
                if (data.should_start_session) {
                  setRecordingStatus(`🎬 Started recording session for ${data.target_sign} (3 seconds)`);
                } else if (data.is_in_session && data.sign_matched) {
                  setRecordingStatus(`📹 Recording session active for ${data.target_sign}...`);
                }
              } else if (lastPrediction) {
                // Keep showing last prediction with reduced opacity
                setPrediction({
                  ...lastPrediction,
                  confidence: lastPrediction.confidence * 0.7,
                  isStale: true
                });
              }
              break;
              
            case 'recording_started':
              setIsRecording(true);
              setRecordingStatus(`Ready to record: ${data.target_sign} (will auto-record for 3s when detected)`);
              break;
              
            case 'recording_stopped':
              setIsRecording(false);
              if (data.result) {
                const reason = data.reason === 'auto_stop_session_complete' ? 'Session completed' : 'Manual stop';
                setRecordingStatus(`✅ Recording saved: ${data.result.frame_count} frames (${reason})`);
              } else {
                setRecordingStatus('Recording stopped');
              }
              break;
              
            default:
              console.log('Unknown message type:', data.type);
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };
      
      wsRef.current.onclose = () => {
        console.log('Disconnected from sign detection backend');
        setIsConnected(false);
        setRecordingStatus('Connection lost - Click retry to reconnect');

        // Attempt to reconnect after 3 seconds
        reconnectTimeoutRef.current = setTimeout(() => {
          console.log('Attempting to reconnect...');
          connect();
        }, 3000);
      };
      
      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        setRecordingStatus('Connection error');
      };
      
    } catch (error) {
      console.error('Error connecting to WebSocket:', error);
      setRecordingStatus('Failed to connect to backend');
    }
  }, []);
  
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (frameIntervalRef.current) {
      clearInterval(frameIntervalRef.current);
    }
    
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    
    setIsConnected(false);
  }, []);
  
  const sendFrame = useCallback((frameData) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      const message = {
        type: 'frame',
        frame: frameData
      };
      wsRef.current.send(JSON.stringify(message));
    }
  }, []);
  
  const startRecording = useCallback((targetSignName) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      const message = {
        type: 'start_recording',
        target_sign: targetSignName
      };
      wsRef.current.send(JSON.stringify(message));
      setTargetSign(targetSignName);
    }
  }, []);
  
  const stopRecording = useCallback(() => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      const message = {
        type: 'stop_recording'
      };
      wsRef.current.send(JSON.stringify(message));
    }
  }, []);
  
  const startFrameCapture = useCallback((webcamRef, interval = 100) => {
    if (frameIntervalRef.current) {
      clearInterval(frameIntervalRef.current);
    }

    frameIntervalRef.current = setInterval(() => {
      if (webcamRef.current && isConnected) {
        const imageSrc = webcamRef.current.getScreenshot();
        if (imageSrc) {
          console.log('Sending frame to backend');
          sendFrame(imageSrc);
        }
      }
    }, interval);
  }, [isConnected, sendFrame]);
  
  const stopFrameCapture = useCallback(() => {
    if (frameIntervalRef.current) {
      clearInterval(frameIntervalRef.current);
      frameIntervalRef.current = null;
    }
  }, []);
  
  // Auto-connect on mount
  useEffect(() => {
    connect();
    
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);
  
  const retryConnection = useCallback(() => {
    setRecordingStatus('Attempting to reconnect...');
    disconnect();
    setTimeout(() => {
      connect();
    }, 1000);
  }, [connect, disconnect]);

  return {
    isConnected,
    prediction,
    lastPrediction,
    predictionHistory,
    isRecording,
    recordingStatus,
    processedFrame,
    signMatched,
    targetSign,
    connect,
    disconnect,
    retryConnection,
    sendFrame,
    startRecording,
    stopRecording,
    startFrameCapture,
    stopFrameCapture
  };
};
