import asyncio
import base64
import cv2
import json
import numpy as np
import os
import pandas as pd
import tensorflow as tf
import mediapipe as mp
from datetime import datetime
from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from typing import Dict, List
import aiofiles
import pyarrow as pa
import pyarrow.parquet as pq
from training import ASLModelTrainer
import logging

# Set up logging
logger = logging.getLogger(__name__)

app = FastAPI(title="Sign Language Detection API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # React app URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables for models and data
interpreter = None
prediction_fn = None
mp_holistic = None
mp_drawing = None
mp_drawing_styles = None
ORD2SIGN = {}

# Create recordings directory
RECORDINGS_DIR = "recordings"
os.makedirs(RECORDINGS_DIR, exist_ok=True)

class SignLanguageDetector:
    def __init__(self):
        self.frame_keypoints = []
        self.confidence_threshold = 0.5
        self.latest_prediction = ""
        self.latest_confidence = 0.0
        
    def extract_keypoints(self, results):
        """Extract keypoints from MediaPipe results"""
        # Face landmarks (468 points)
        face = np.array([[res.x, res.y, res.z] for res in results.face_landmarks.landmark]).flatten() if results.face_landmarks else np.full(468*3, np.nan)
        # Left hand landmarks (21 points)
        lh = np.array([[res.x, res.y, res.z] for res in results.left_hand_landmarks.landmark]).flatten() if results.left_hand_landmarks else np.full(21*3, np.nan)
        # Pose landmarks (33 points)
        pose = np.array([[res.x, res.y, res.z] for res in results.pose_landmarks.landmark]).flatten() if results.pose_landmarks else np.full(33*3, np.nan)
        # Right hand landmarks (21 points)
        rh = np.array([[res.x, res.y, res.z] for res in results.right_hand_landmarks.landmark]).flatten() if results.right_hand_landmarks else np.full(21*3, np.nan)
        
        # Concatenate all keypoints
        all_keypoints = np.concatenate([face, lh, pose, rh])
        # Reshape to (543, 3)
        reshaped_keypoints = np.reshape(all_keypoints, (543, 3))
        return reshaped_keypoints
    
    def mediapipe_detection(self, frame, model):
        """Process frame with MediaPipe"""
        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        frame.flags.writeable = False
        results = model.process(frame)
        frame.flags.writeable = True
        frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
        return frame, results
    
    def draw_landmarks(self, image, results):
        """Draw landmarks on the image"""
        # Draw face landmarks
        mp_drawing.draw_landmarks(
            image, results.face_landmarks, mp_holistic.FACEMESH_CONTOURS,
            landmark_drawing_spec=None,
            connection_drawing_spec=mp_drawing_styles.get_default_face_mesh_contours_style()
        )
        # Draw pose landmarks
        mp_drawing.draw_landmarks(
            image, results.pose_landmarks, mp_holistic.POSE_CONNECTIONS,
            landmark_drawing_spec=mp_drawing_styles.get_default_pose_landmarks_style()
        )
        # Draw hand landmarks
        mp_drawing.draw_landmarks(
            image, results.left_hand_landmarks, mp_holistic.HAND_CONNECTIONS,
            landmark_drawing_spec=mp_drawing_styles.get_default_hand_landmarks_style(),
            connection_drawing_spec=mp_drawing_styles.get_default_hand_connections_style()
        )
        mp_drawing.draw_landmarks(
            image, results.right_hand_landmarks, mp_holistic.HAND_CONNECTIONS,
            landmark_drawing_spec=mp_drawing_styles.get_default_hand_landmarks_style(),
            connection_drawing_spec=mp_drawing_styles.get_default_hand_connections_style()
        )
    
    def process_frame(self, frame_data, model):
        """Process a single frame and return prediction"""
        try:
            # Decode base64 image
            img_data = base64.b64decode(frame_data.split(',')[1])
            nparr = np.frombuffer(img_data, np.uint8)
            frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            if frame is None:
                print("Failed to decode frame")
                return None, None, None

            print(f"Processing frame: {frame.shape}, buffer size: {len(self.frame_keypoints)}")
            
            # Process with MediaPipe
            processed_frame, results = self.mediapipe_detection(frame, model)
            
            # Draw landmarks
            self.draw_landmarks(processed_frame, results)
            
            # Extract keypoints
            keypoints = self.extract_keypoints(results)
            self.frame_keypoints.append(keypoints)
            self.frame_keypoints = self.frame_keypoints[-30:]  # Keep last 30 frames
            
            prediction_result = None
            
            # Make prediction if we have enough frames
            if len(self.frame_keypoints) >= 30:
                res = np.expand_dims(self.frame_keypoints, axis=0)[0].astype(np.float32)
                print(f"Making prediction with shape: {res.shape}")

                prediction = prediction_fn(inputs=res)
                probabilities = prediction['outputs'][0]
                predicted_sign = np.argmax(probabilities)
                confidence = probabilities[predicted_sign]

                # Always show prediction (even if low confidence)
                self.latest_prediction = ORD2SIGN[predicted_sign]
                self.latest_confidence = float(confidence)

                print(f"Predicted: {self.latest_prediction} with confidence: {confidence:.3f}")

                prediction_result = {
                    "sign": self.latest_prediction,
                    "confidence": self.latest_confidence
                }

                # Reset frame buffer after prediction
                self.frame_keypoints = []
            
            # Encode processed frame back to base64
            _, buffer = cv2.imencode('.jpg', processed_frame)
            processed_frame_b64 = base64.b64encode(buffer).decode('utf-8')

            return processed_frame_b64, prediction_result, frame, keypoints
            
        except Exception as e:
            print(f"Error processing frame: {e}")
            return None, None, None, None

# Global detector instance
detector = SignLanguageDetector()

# Recording management
class RecordingManager:
    def __init__(self):
        self.active_recordings = {}  # websocket_id -> recording_data
        self.min_frames_for_recording = 30  # Minimum frames to consider it a valid recording
        self.recording_duration = 3.0  # Record for 3 seconds when sign is detected

    def start_recording(self, websocket_id: str, target_sign: str):
        """Start recording for a specific sign"""
        # Normalize sign name to lowercase for consistency
        normalized_sign = target_sign.lower()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        video_filename = f"{normalized_sign}_{timestamp}.avi"
        parquet_filename = f"{normalized_sign}_{timestamp}.parquet"
        video_filepath = os.path.join(RECORDINGS_DIR, video_filename)
        parquet_filepath = os.path.join(RECORDINGS_DIR, parquet_filename)

        self.active_recordings[websocket_id] = {
            "target_sign": normalized_sign,  # Store normalized sign name
            "original_sign": target_sign,    # Keep original for display
            "video_filepath": video_filepath,
            "parquet_filepath": parquet_filepath,
            "frames": [],
            "landmark_data": [],  # Store landmark data for parquet
            "started_at": datetime.now(),
            "fourcc": cv2.VideoWriter_fourcc(*'XVID'),
            "writer": None,
            "frame_size": None,
            "is_recording_session": False,  # Whether we're in an active recording session
            "session_start_time": None,     # When the current session started
            "last_detection_time": None     # When we last detected the sign
        }

        return parquet_filepath

    def add_frame(self, websocket_id: str, frame, landmarks=None):
        """Add frame and landmark data to active recording"""
        if websocket_id in self.active_recordings:
            recording = self.active_recordings[websocket_id]
            current_time = datetime.now()

            if recording["writer"] is None:
                # Initialize video writer with first frame
                h, w = frame.shape[:2]
                recording["frame_size"] = (w, h)
                try:
                    recording["writer"] = cv2.VideoWriter(
                        recording["video_filepath"],
                        recording["fourcc"],
                        10.0,  # FPS - changed to 10.0 to match frontend frame rate (100ms interval)
                        (w, h)
                    )
                    if not recording["writer"].isOpened():
                        print(f"❌ Failed to initialize video writer for {recording['video_filepath']}")
                        recording["writer"] = None
                        return
                    print(f"📹 Initialized video writer: {w}x{h} at 10 FPS")
                except Exception as e:
                    print(f"❌ Error initializing video writer: {e}")
                    recording["writer"] = None
                    return

            recording["writer"].write(frame)
            recording["frames"].append(len(recording["frames"]))

            # Add landmark data if provided
            if landmarks is not None:
                recording["landmark_data"].append(landmarks)

            print(f"📸 Frame {len(recording['frames'])} written to recording")
        else:
            print(f"⚠️ No active recording for websocket {websocket_id}")

    def start_recording_session(self, websocket_id: str):
        """Start a 3-second recording session"""
        if websocket_id in self.active_recordings:
            recording = self.active_recordings[websocket_id]
            recording["is_recording_session"] = True
            recording["session_start_time"] = datetime.now()
            print(f"🎬 Started 3-second recording session for {recording['target_sign']}")

    def should_stop_recording_session(self, websocket_id: str):
        """Check if we should stop the current recording session"""
        if websocket_id in self.active_recordings:
            recording = self.active_recordings[websocket_id]
            if recording["is_recording_session"] and recording["session_start_time"]:
                elapsed_time = (datetime.now() - recording["session_start_time"]).total_seconds()
                if elapsed_time >= self.recording_duration:
                    print(f"⏰ Recording session complete after {elapsed_time:.1f}s")
                    return True
        return False

    def stop_recording(self, websocket_id: str):
        """Stop and save recording"""
        if websocket_id in self.active_recordings:
            recording = self.active_recordings[websocket_id]

            if recording["writer"]:
                recording["writer"].release()

            frame_count = len(recording["frames"])
            landmark_count = len(recording["landmark_data"])
            duration = (datetime.now() - recording["started_at"]).total_seconds()

            # Save landmark data to parquet file if we have enough frames
            parquet_saved = False
            if landmark_count >= self.min_frames_for_recording:
                try:
                    # Convert landmark data to the format expected by training
                    # Each landmark is (543, 3) array, we need to flatten it for parquet
                    landmark_rows = []
                    for frame_idx, landmarks in enumerate(recording["landmark_data"]):
                        # landmarks is (543, 3) array
                        for landmark_idx in range(landmarks.shape[0]):
                            landmark_rows.append({
                                'frame': frame_idx,
                                'row_id': frame_idx * 543 + landmark_idx,
                                'type': self._get_landmark_type(landmark_idx),
                                'landmark_index': landmark_idx,
                                'x': landmarks[landmark_idx, 0],
                                'y': landmarks[landmark_idx, 1],
                                'z': landmarks[landmark_idx, 2]
                            })

                    # Create DataFrame and save as parquet
                    df = pd.DataFrame(landmark_rows)
                    df.to_parquet(recording["parquet_filepath"], index=False)
                    parquet_saved = True
                    print(f"💾 Saved landmark data: {landmark_count} frames to {recording['parquet_filepath']}")

                    # Update train.csv with new recording
                    self._update_train_csv(recording["parquet_filepath"], recording["target_sign"])

                except Exception as e:
                    print(f"❌ Error saving parquet file: {e}")

            # Only consider it a valid recording if we have enough frames
            if frame_count >= self.min_frames_for_recording:
                print(f"✅ Valid recording: {frame_count} frames, {duration:.1f}s duration")
            else:
                print(f"⚠️ Short recording: {frame_count} frames, {duration:.1f}s duration (minimum: {self.min_frames_for_recording})")

            result = {
                "video_filepath": recording["video_filepath"],
                "parquet_filepath": recording["parquet_filepath"],
                "target_sign": recording["target_sign"],
                "frame_count": frame_count,
                "landmark_count": landmark_count,
                "duration": duration,
                "is_valid": frame_count >= self.min_frames_for_recording,
                "parquet_saved": parquet_saved
            }

            del self.active_recordings[websocket_id]
            return result

        return None

    def _get_landmark_type(self, landmark_idx):
        """Get the type of landmark based on index"""
        if landmark_idx < 468:
            return 'face'
        elif landmark_idx < 489:  # 468 + 21
            return 'left_hand'
        elif landmark_idx < 522:  # 468 + 21 + 33
            return 'pose'
        else:  # 522 to 543
            return 'right_hand'

    def _update_train_csv(self, parquet_filepath, sign_name):
        """Update train.csv with new recording"""
        try:
            train_csv_path = os.path.join("data", "train.csv")

            # Create relative path for the parquet file
            # The parquet file is in recordings/, but we want to reference it relative to data/
            relative_path = os.path.relpath(parquet_filepath, start=".")

            # Generate a unique sequence_id and participant_id
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            participant_id = 99999  # Use a high number to distinguish from original data
            sequence_id = int(timestamp)

            # Ensure sign name is lowercase for consistency
            normalized_sign = sign_name.lower()

            # Create new row
            new_row = {
                'path': relative_path,
                'participant_id': participant_id,
                'sequence_id': sequence_id,
                'sign': normalized_sign
            }

            # Append to train.csv
            if os.path.exists(train_csv_path):
                df = pd.read_csv(train_csv_path)
                df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)
            else:
                df = pd.DataFrame([new_row])

            df.to_csv(train_csv_path, index=False)
            print(f"📝 Updated train.csv with new recording: {normalized_sign} -> {relative_path}")

        except Exception as e:
            print(f"❌ Error updating train.csv: {e}")
            import traceback
            traceback.print_exc()

    def is_recording(self, websocket_id: str):
        """Check if currently recording"""
        return websocket_id in self.active_recordings

    def is_in_recording_session(self, websocket_id: str):
        """Check if currently in an active recording session"""
        if websocket_id in self.active_recordings:
            return self.active_recordings[websocket_id]["is_recording_session"]
        return False

    def get_target_sign(self, websocket_id: str):
        """Get target sign for active recording"""
        if websocket_id in self.active_recordings:
            return self.active_recordings[websocket_id]["target_sign"]
        return None

# Global recording manager
recording_manager = RecordingManager()

@app.on_event("startup")
async def startup_event():
    """Initialize models on startup"""
    global interpreter, prediction_fn, mp_holistic, mp_drawing, mp_drawing_styles, ORD2SIGN
    
    try:
        # Load TensorFlow Lite model - use only the local version
        model_path = os.path.join("models", "model.tflite")
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"No model.tflite found at {model_path}")

        print(f"Loading model from: {model_path}")
        interpreter = tf.lite.Interpreter(model_path=model_path)
        found_signatures = list(interpreter.get_signature_list().keys())
        prediction_fn = interpreter.get_signature_runner("serving_default")
        print(f"Model signatures: {found_signatures}")

        # Load MediaPipe
        mp_holistic = mp.solutions.holistic
        mp_drawing = mp.solutions.drawing_utils
        mp_drawing_styles = mp.solutions.drawing_styles

        # Load sign labels - use only the local version
        train_path = os.path.join("data", "train.csv")
        if not os.path.exists(train_path):
            raise FileNotFoundError(f"No train.csv found at {train_path}")

        print(f"Loading labels from: {train_path}")
        train = pd.read_csv(train_path)
        train['sign_ord'] = train['sign'].astype('category').cat.codes

        # Use the same mapping as the working Streamlit app
        ORD2SIGN = train[['sign_ord', 'sign']].set_index('sign_ord').squeeze().to_dict()

        print("Models loaded successfully!")
        print(f"Available signs: {len(ORD2SIGN)}")
        print(f"Sample signs: {list(ORD2SIGN.values())[:10]}")

    except Exception as e:
        print(f"Error loading models: {e}")
        import traceback
        traceback.print_exc()

@app.websocket("/ws/detect")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time sign detection"""
    await websocket.accept()

    # Generate unique ID for this connection
    websocket_id = f"ws_{datetime.now().timestamp()}"

    # Initialize MediaPipe model for this connection
    holistic_model = mp_holistic.Holistic(
        min_detection_confidence=0.5,
        min_tracking_confidence=0.5
    )

    try:
        while True:
            # Receive data from client
            data = await websocket.receive_text()
            message = json.loads(data)

            if message.get("type") == "start_recording":
                # Start recording for target sign
                target_sign = message.get("target_sign")
                filepath = recording_manager.start_recording(websocket_id, target_sign)
                print(f"🎬 Started recording for sign: {target_sign} -> {filepath}")

                response = {
                    "type": "recording_started",
                    "target_sign": target_sign,
                    "filepath": filepath,
                    "timestamp": datetime.now().isoformat()
                }
                await websocket.send_text(json.dumps(response))

            elif message.get("type") == "stop_recording":
                # Stop recording
                result = recording_manager.stop_recording(websocket_id)
                if result:
                    print(f"💾 Stopped recording: {result['frame_count']} frames, {result['landmark_count']} landmarks saved")
                    print(f"   Video: {result['video_filepath']}")
                    print(f"   Landmarks: {result['parquet_filepath']}")
                else:
                    print("⚠️ No active recording to stop")

                response = {
                    "type": "recording_stopped",
                    "result": result,
                    "timestamp": datetime.now().isoformat()
                }
                await websocket.send_text(json.dumps(response))

            elif message.get("type") == "frame":
                # Process frame
                processed_frame, prediction, original_frame, keypoints = detector.process_frame(
                    message['frame'], holistic_model
                )

                if processed_frame and original_frame is not None:
                    # Check if we should record this frame
                    is_recording = recording_manager.is_recording(websocket_id)
                    is_in_session = recording_manager.is_in_recording_session(websocket_id)
                    target_sign = recording_manager.get_target_sign(websocket_id)
                    sign_matched = False
                    should_start_session = False

                    # ALWAYS record the frame if we're in a recording session
                    if is_in_session:
                        recording_manager.add_frame(websocket_id, original_frame, keypoints)
                        sign_matched = True
                        print(f"📹 Recording frame during session (frame count: {len(recording_manager.active_recordings[websocket_id]['frames'])})")

                    if is_recording and prediction and target_sign:
                        predicted_sign = prediction["sign"].lower()
                        target_sign_lower = target_sign.lower()
                        confidence = prediction["confidence"]

                        # Check if predicted sign matches target sign AND confidence is high enough
                        if predicted_sign == target_sign_lower and confidence > detector.confidence_threshold:
                            # If we're not in a recording session, start one
                            if not is_in_session:
                                should_start_session = True
                                recording_manager.start_recording_session(websocket_id)
                                print(f"🎬 Started 3-second recording session for {target_sign} (confidence: {confidence:.3f})")
                        else:
                            # Log why frame wasn't recorded
                            if predicted_sign != target_sign_lower:
                                print(f"❌ Sign mismatch: predicted '{predicted_sign}' vs target '{target_sign_lower}'")
                            elif confidence <= detector.confidence_threshold:
                                print(f"❌ Low confidence: {confidence:.3f} <= {detector.confidence_threshold}")
                            else:
                                print(f"❌ Unknown reason for not recording")

                    # Check if we should stop the current recording session
                    if is_in_session and recording_manager.should_stop_recording_session(websocket_id):
                        print(f"⏰ Auto-stopping recording session after {recording_manager.recording_duration}s")
                        result = recording_manager.stop_recording(websocket_id)
                        if result:
                            auto_stop_response = {
                                "type": "recording_stopped",
                                "result": result,
                                "reason": "auto_stop_session_complete",
                                "timestamp": datetime.now().isoformat()
                            }
                            await websocket.send_text(json.dumps(auto_stop_response))

                    response = {
                        "type": "frame_processed",
                        "processed_frame": f"data:image/jpeg;base64,{processed_frame}",
                        "prediction": prediction,
                        "is_recording": is_recording,
                        "is_in_session": is_in_session,
                        "target_sign": target_sign,
                        "sign_matched": sign_matched,
                        "should_start_session": should_start_session,
                        "timestamp": datetime.now().isoformat()
                    }

                    await websocket.send_text(json.dumps(response))

    except WebSocketDisconnect:
        print(f"Client {websocket_id} disconnected")
        # Clean up any active recording
        recording_manager.stop_recording(websocket_id)
    except Exception as e:
        print(f"WebSocket error: {e}")
    finally:
        holistic_model.close()

@app.get("/")
async def root():
    return {"message": "Sign Language Detection API is running"}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "models_loaded": interpreter is not None,
        "available_signs": len(ORD2SIGN) if ORD2SIGN else 0
    }

@app.get("/signs")
async def get_available_signs():
    """Get list of available signs that can be detected"""
    return {
        "signs": list(ORD2SIGN.values()) if ORD2SIGN else [],
        "count": len(ORD2SIGN) if ORD2SIGN else 0
    }

@app.get("/recordings")
async def list_recordings():
    """List all recorded videos"""
    recordings = []
    if os.path.exists(RECORDINGS_DIR):
        for filename in os.listdir(RECORDINGS_DIR):
            if filename.endswith(('.mp4', '.avi')):  # Show both old .mp4 and new .avi files
                filepath = os.path.join(RECORDINGS_DIR, filename)
                stat = os.stat(filepath)
                recordings.append({
                    "filename": filename,
                    "filepath": filepath,
                    "size": stat.st_size,
                    "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                    "file_type": filename.split('.')[-1]
                })

    return {"recordings": recordings, "count": len(recordings)}

@app.post("/train")
async def start_training():
    """Start model training with recorded data"""
    try:
        trainer = ASLModelTrainer()

        # Check if we have enough data
        train_csv_path = os.path.join("data", "train.csv")
        if not os.path.exists(train_csv_path):
            return {"error": "No training data found. Please record some signs first."}

        df = pd.read_csv(train_csv_path)
        if len(df) < 10:
            return {"error": f"Not enough training data. Found {len(df)} samples, need at least 10."}

        # Start training in background (for now, synchronous)
        result = trainer.train_model()

        if result:
            return {
                "status": "success",
                "message": "Model training completed successfully",
                "model_path": result["model_path"],
                "tflite_path": result["tflite_path"],
                "final_accuracy": result["final_accuracy"],
                "final_val_accuracy": result["final_val_accuracy"]
            }
        else:
            return {"error": "Training failed"}

    except Exception as e:
        logger.error(f"Training error: {e}")
        return {"error": f"Training failed: {str(e)}"}

@app.get("/training/status")
async def get_training_status():
    """Get current training status"""
    # For now, just return basic info about available data
    try:
        train_csv_path = os.path.join("data", "train.csv")
        if os.path.exists(train_csv_path):
            df = pd.read_csv(train_csv_path)
            sign_counts = df['sign'].value_counts().to_dict()

            return {
                "total_samples": len(df),
                "unique_signs": len(sign_counts),
                "sign_distribution": sign_counts,
                "ready_for_training": len(df) >= 10
            }
        else:
            return {
                "total_samples": 0,
                "unique_signs": 0,
                "sign_distribution": {},
                "ready_for_training": False
            }
    except Exception as e:
        return {"error": f"Failed to get training status: {str(e)}"}

if __name__ == "__main__":
    import uvicorn
    print("Starting server...")
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
