import React, { useState } from 'react';
import styled from 'styled-components';
import HomePage from './components/HomePage';
import TrainingPage from './components/TrainingPage';
import AboutPage from './components/AboutPage';
import ContactPage from './components/ContactPage';
import './App.css';

const AppContainer = styled.div`
  min-height: 100vh;
  background: var(--bg-primary);
  font-family: var(--font-secondary);
  overflow-x: hidden;
  position: relative;

  /* Subtle background pattern for visual interest */
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 25% 25%, var(--primary-50) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, var(--secondary-50) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
  }
`;

const PageWrapper = styled.div`
  position: relative;
  z-index: 1;
  min-height: 100vh;
`;

function App() {
  const [currentPage, setCurrentPage] = useState('home');

  const navigateToTraining = () => {
    setCurrentPage('training');
  };

  const navigateToAbout = () => {
    setCurrentPage('about');
  };

  const navigateToContact = () => {
    setCurrentPage('contact');
  };

  const navigateToHome = () => {
    setCurrentPage('home');
  };

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'training':
        return <TrainingPage onBackToHome={navigateToHome} />;
      case 'about':
        return <AboutPage onBackToHome={navigateToHome} />;
      case 'contact':
        return <ContactPage onBackToHome={navigateToHome} />;
      default:
        return (
          <HomePage
            onStartTraining={navigateToTraining}
            onNavigateToAbout={navigateToAbout}
            onNavigateToContact={navigateToContact}
          />
        );
    }
  };

  return (
    <AppContainer>
      <PageWrapper>
        {renderCurrentPage()}
      </PageWrapper>
    </AppContainer>
  );
}

export default App;
